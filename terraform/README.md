This README provides instructions for deploying the AWS security configurations using Terraform in this repository.

## Note

Always commit `.terraform.lock.hcl` after deployment.

## Prerequisites

- AWS CLI installed and configured
- Terraform installed (version 1.11.x recommended)

## AWS SSO Configuration

First, configure AWS SSO access:

```bash
aws configure sso
```

Use the following settings:
- **Session name:** `aent`
- **Start URL:** `https://metawin.awsapps.com/start/#`
- **Region:** `eu-west-1`
- **SSO Profile:** `OrgAdministratorAccess-************` (management-account) 
`security-admin` (org-wide security-account) `security-dev` (security-account)

## Terraform deployer role

Non-org wide configuration should be deployed through terraform deployer role, configure:

```bash
aws sts assume-role --role-arn "arn:aws:iam::************:role/security-terraform-deployer" --role-session-name "security-terraform-deployer"
```

## Deploying with Terraform

Navigate to the security account environment directory:

```bash
cd terraform/security-account/environments/prod
```

Initialize Terraform to download required providers:

```bash
terraform init
```

Review the planned changes:

```bash
terraform plan
```

Apply the changes to deploy resources:

```bash
terraform apply
```

## Resources Deployed

Organization-wide services should be deployed through Administrator (`aws configure sso`) role. Do not deploy any of these to AWS dev account.

- **security-terraform-deployer-role**: Custom role for Terraform deployment
- **CloudTrail**: Organization-wide audit logging
- **GuardDuty**: Threat detection service
- **AWS Config**: Configuration management and compliance
- **SecurityHub**: Security posture management

The rest should be deployed through **security-terraform-deploy** role, permissions should be adjusted as needed and follow least privilege principle (no wildcards, be specific).

Detailed Configurations
- **Organization-wide CloudTrail**
  - Insights and management events
  - CloudWatch Logs group with 7-day retention
  - S3 bucket and policies (90 days compliance retention, after 90 days glacier)
  - KMS key for encryption
- **Organization-wide GuardDuty**
  - Basic features (detection - DNS Logs, VPC Flow Logs, CloudTrail Management Events)
  - EC2 malware protection
- **Organization-wide AWS Config**
  - Organization aggregator for centralized visibility
  - S3 bucket with KMS encryption for configuration snapshots
- **Organization-wide SecurityHub**
  - Centralized security findings
  - Integration with GuardDuty and AWS Config
  - Standard compliance frameworks enabled (AWS Foundational Security Best Practices, CIS)

# Managemenet account setup

In AWS Organization trusted access has to be enabled for SecurityHub, Config, Inspector, CloudTrail, IAM Access Analyzer.
`aent-security-prod` is a delegated administrator for SecurityHub, Config, Inspector, StackSets and CloudTrail.

Quick setup of delegation:

```bash
# Register security account as Config delegated administrator
aws organizations register-delegated-administrator \
  --account-id ************ \
  --service-principal config.amazonaws.com \
  --profile OrgAdministratorAccess-************

# Register security account as SecurityHub delegated administrator
# This one should be deployed from Terraform management-account, but I'm adding it here for completeness
aws organizations register-delegated-administrator \
  --account-id ************ \
  --service-principal securityhub.amazonaws.com \
  --profile OrgAdministratorAccess-************

# Register security account as GuardDuty delegated administrator
aws organizations register-delegated-administrator \
  --account-id ************ \
  --service-principal guardduty.amazonaws.com \
  --profile OrgAdministratorAccess-************

# Register security account as CloudTrail delegated administrator
aws organizations register-delegated-administrator \
  --account-id ************ \
  --service-principal cloudtrail.amazonaws.com \
  --profile OrgAdministratorAccess-************

# Register security account as CloudFormation StackSets delegated administrator
aws organizations register-delegated-administrator \
  --account-id ************ \
  --service-principal cloudformation.amazonaws.com \
  --profile OrgAdministratorAccess-************
```

**Note**: After deployment, ensure all existing organization accounts are added to GuardDuty manually as auto-enable only applies to new accounts. Terraform has limited support for enabling existing accounts.