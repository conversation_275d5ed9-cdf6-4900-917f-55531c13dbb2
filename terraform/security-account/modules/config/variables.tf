variable "config_logs_bucket_name" {
  description = "Name of the S3 bucket where AWS Config logs will be stored"
  type        = string
}

variable "config_aggregator_role" {
  description = "Name of the IAM role for AWS Config to assume"
  type        = string
}

variable "config_aggregator_name" {
  description = "Name of the AWS Config aggregator"
  type        = string
}


variable "config_access_arns" {
  description = "List of ARNs for IAM users or roles that are granted access to manage Config resources, including the S3 bucket and KMS key."
  type        = list(string)
}

variable "organization_id" {
  description = "ID of the AWS organization"
}
