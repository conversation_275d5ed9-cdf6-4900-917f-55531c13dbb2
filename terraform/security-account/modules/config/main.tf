# KMS Key for S3 Bucket Encryption
resource "aws_kms_key" "config_s3_key" {
  description             = "KMS key for encrypting AWS Config logs in S3 bucket ${var.config_logs_bucket_name}"
  enable_key_rotation     = true
}

resource "aws_kms_key_policy" "config_s3_key_policy" {
  key_id = aws_kms_key.config_s3_key.id
  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Sid    = "EnableIAMUserPermissions"
        Effect = "Allow"
        Principal = { AWS = var.config_access_arns }
        Action   = "kms:*"
        Resource = "*"
      },
      {
        Sid    = "AllowConfigServicePrincipalToUseKey"
        Effect = "Allow"
        Principal = { Service = "config.amazonaws.com" }
        Action = [
          "kms:GenerateDataKey*",
          "kms:Decrypt"
        ]
        Resource = "*"
        Condition = {
          StringEquals = {
            "aws:SourceOrgID": var.organization_id
          }
        }
      }
    ]
  })
}

resource "aws_kms_alias" "config_s3_key_alias" {
  name          = "alias/${var.config_logs_bucket_name}-key"
  target_key_id = aws_kms_key.config_s3_key.key_id
}

# IAM Role for AWS Config
resource "aws_iam_role" "config_aggregator_role" {
  name               = var.config_aggregator_role
  assume_role_policy = data.aws_iam_policy_document.assume_role_policy.json
}

# IAM Policy Document for AWS Config config_aggregator_role to Assume Role
data "aws_iam_policy_document" "assume_role_policy" {
  statement {
    effect = "Allow"

    principals {
      type        = "Service"
      identifiers = ["config.amazonaws.com"]
    }

    actions = ["sts:AssumeRole"]
  }
}

# Attach AWS Config Managed Policies to IAM Role - it will read data across organization
resource "aws_iam_role_policy_attachment" "aws_config_aggregator_role_policy_attachment" {
  role       = aws_iam_role.config_aggregator_role.name
  policy_arn = "arn:aws:iam::aws:policy/service-role/AWSConfigRoleForOrganizations"
}

# Organization-Wide Aggregator for AWS Config
resource "aws_config_configuration_aggregator" "config_aggregator" {
  depends_on = [aws_iam_role_policy_attachment.aws_config_aggregator_role_policy_attachment]
  name       = var.config_aggregator_name

  organization_aggregation_source {
    all_regions = true
    role_arn = aws_iam_role.config_aggregator_role.arn
  }
}

# S3 Bucket for AWS Config Logs
resource "aws_s3_bucket" "config_logs" {
  bucket = var.config_logs_bucket_name
}

resource "aws_s3_bucket_policy" "config_logs_policy" {
  bucket = aws_s3_bucket.config_logs.id
  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Sid    = "LimitGetObject"
        Effect = "Allow"
        Principal = { AWS = var.config_access_arns }
        Action = [
          "s3:GetObject",
          "s3:GetObjectVersion"
        ]
        Resource = "${aws_s3_bucket.config_logs.arn}/*"
      },
      {
        Sid    = "AllowConfigPutObject"
        Effect = "Allow"
        Principal = {
          Service = "config.amazonaws.com"
        }
        Action   = "s3:PutObject"
        Resource = "${aws_s3_bucket.config_logs.arn}/*"
        Condition = {
          StringEquals = {
            "s3:x-amz-acl"    = "bucket-owner-full-control"
            "aws:SourceOrgID" = var.organization_id
          }
        }
      },
      {
        Sid    = "AllowConfigGetBucketAcl"
        Effect = "Allow"
        Principal = {
          Service = "config.amazonaws.com"
        }
        Action   = "s3:GetBucketAcl"
        Resource = aws_s3_bucket.config_logs.arn
        Condition = {
          StringEquals = {
            "aws:SourceOrgID" = var.organization_id
          }
        }
      },
      {
        Sid    = "AllowMemberConfigListBucketForExistenceCheck"
        Effect = "Allow"
        Principal = {
          AWS = "*"
        }
        Action   = "s3:ListBucket"
        Resource = aws_s3_bucket.config_logs.arn
        Condition = {
          StringEquals = {
            "aws:PrincipalOrgID" = var.organization_id
          },
          ArnLike = {
            "aws:PrincipalArn": "arn:aws:iam::*:role/aws-service-role/config.amazonaws.com/AWSServiceRoleForConfig"
          }
        }
      }
    ]
  })
}

# S3 Bucket KMS encryption
resource "aws_s3_bucket_server_side_encryption_configuration" "config_log_encryption" {
  bucket = aws_s3_bucket.config_logs.id

  rule {
    apply_server_side_encryption_by_default {
      sse_algorithm     = "aws:kms"
      kms_master_key_id = aws_kms_key.config_s3_key.arn
    }
    bucket_key_enabled = true
  }
}

# Block Public Access to S3 Bucket
resource "aws_s3_bucket_public_access_block" "config_logs_public_access_block" {
  bucket = aws_s3_bucket.config_logs.id

  block_public_acls       = true
  block_public_policy     = true
  ignore_public_acls      = true
  restrict_public_buckets = true
}
