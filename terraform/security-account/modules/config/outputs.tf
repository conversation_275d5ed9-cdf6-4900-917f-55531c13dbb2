output "config_logs_bucket_name" {
  description = "The name of the S3 bucket used for AWS Config logs"
  value       = aws_s3_bucket.config_logs.bucket
}

output "config_aggregator_role_arn" {
  description = "The ARN of the IAM role used by AWS Config"
  value       = aws_iam_role.config_aggregator_role.arn
}

output "config_kms_key_arn" {
  description = "The ARN of the KMS key for Config S3 encryption"
  value       = aws_kms_key.config_s3_key.arn
}

output "config_aggregator_name" {
  description = "The name of the AWS Config organization-wide aggregator"
  value       = aws_config_configuration_aggregator.config_aggregator.name
}
