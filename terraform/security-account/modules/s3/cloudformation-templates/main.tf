resource "aws_s3_bucket" "template_bucket" {
  bucket = var.bucket_name
  force_destroy = false
}

resource "aws_s3_bucket_versioning" "template_versioning" {
  bucket = aws_s3_bucket.template_bucket.id
  versioning_configuration {
    status = "Enabled"
  }
}

resource "aws_s3_bucket_server_side_encryption_configuration" "template_encryption" {
  bucket = aws_s3_bucket.template_bucket.id

  rule {
    apply_server_side_encryption_by_default {
      sse_algorithm = "AES256"
    }
    bucket_key_enabled = true
  }
}

resource "aws_s3_bucket_public_access_block" "template_public_access_block" {
  bucket = aws_s3_bucket.template_bucket.id

  block_public_acls       = true
  block_public_policy     = true
  ignore_public_acls      = true
  restrict_public_buckets = true
}

resource "aws_s3_bucket_policy" "template_bucket_policy" {
  bucket = aws_s3_bucket.template_bucket.id
  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Sid       = "EnforceSSLOnly"
        Effect    = "Deny"
        Principal = "*"
        Action    = "s3:*"
        Resource = [
          aws_s3_bucket.template_bucket.arn,
          "${aws_s3_bucket.template_bucket.arn}/*"
        ]
        Condition = {
          Bool = {
            "aws:SecureTransport": "false"
          }
        }
      },
      {
        Sid       = "AllowManagementAccountToReadTemplates"
        Effect    = "Allow"
        Principal = {
          AWS = "arn:aws:iam::************:root"
        },
        Action    = [
          "s3:GetObject",
        ],
        Resource = [
          "${aws_s3_bucket.template_bucket.arn}/config-recorder/*"
        ],
        Condition = {
          StringEquals = {
            "aws:PrincipalOrgID": var.organization_id
          }
        }
      }
    ]
  })
}
