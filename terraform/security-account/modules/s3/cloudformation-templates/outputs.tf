output "bucket_id" {
  description = "The name of the bucket"
  value       = aws_s3_bucket.template_bucket.id
}

output "bucket_arn" {
  description = "The ARN of the bucket"
  value       = aws_s3_bucket.template_bucket.arn
}

output "bucket_domain_name" {
  description = "The domain name of the bucket"
  value       = aws_s3_bucket.template_bucket.bucket_domain_name
}

output "template_url_prefix" {
  description = "Prefix for CloudFormation template URLs"
  value       = "https://${aws_s3_bucket.template_bucket.bucket_regional_domain_name}"
}
