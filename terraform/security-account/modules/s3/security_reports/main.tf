resource "aws_s3_bucket" "security_reports_bucket" {
  bucket = var.bucket_name
  force_destroy = false
}

resource "aws_s3_bucket_versioning" "security_reports_versioning" {
  bucket = aws_s3_bucket.security_reports_bucket.id
  versioning_configuration {
    status = "Enabled"
  }
}

resource "aws_s3_bucket_server_side_encryption_configuration" "security_reports_encryption" {
  bucket = aws_s3_bucket.security_reports_bucket.id

  rule {
    apply_server_side_encryption_by_default {
      sse_algorithm = "AES256"
    }
    bucket_key_enabled = true
  }
}

resource "aws_s3_bucket_public_access_block" "security_reports_public_access_block" {
  bucket = aws_s3_bucket.security_reports_bucket.id

  block_public_acls       = true
  block_public_policy     = true
  ignore_public_acls      = true
  restrict_public_buckets = true
}

resource "aws_s3_bucket_policy" "security_reports_bucket_policy" {
  bucket = aws_s3_bucket.security_reports_bucket.id
  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Sid       = "EnforceSSLOnly"
        Effect    = "Deny"
        Principal = "*"
        Action    = "s3:*"
        Resource = [
          aws_s3_bucket.security_reports_bucket.arn,
          "${aws_s3_bucket.security_reports_bucket.arn}/*"
        ]
        Condition = {
          Bool = {
            "aws:SecureTransport": "false"
          }
        }
      }
    ]
  })
}
