terraform {
  required_providers {
    aws = {
      source = "hashicorp/aws"
    }
  }
}

resource "aws_guardduty_detector" "guardduty_detector" {
  enable = true
}

resource "aws_guardduty_detector_feature" "ebs_malware_protection_feature" {
  detector_id = aws_guardduty_detector.guardduty_detector.id
  name        = "EBS_MALWARE_PROTECTION"
  status      = "ENABLED"
}

resource "aws_guardduty_detector_feature" "eks_runtime_monitoring_feature" {
  detector_id = aws_guardduty_detector.guardduty_detector.id
  name        = "EKS_RUNTIME_MONITORING"
  status      = "DISABLED"
}

resource "aws_guardduty_detector_feature" "rds_login_events_feature" {
  detector_id = aws_guardduty_detector.guardduty_detector.id
  name        = "RDS_LOGIN_EVENTS"
  status      = "DISABLED"
}

resource "aws_guardduty_detector_feature" "eks_audit_logs_feature" {
  detector_id = aws_guardduty_detector.guardduty_detector.id
  name        = "EKS_AUDIT_LOGS"
  status      = "DISABLED"
}

resource "aws_guardduty_detector_feature" "s3_data_events_feature" {
  detector_id = aws_guardduty_detector.guardduty_detector.id
  name        = "S3_DATA_EVENTS"
  status      = "DISABLED"
}

resource "aws_guardduty_detector_feature" "lambda_network_logs_feature" {
  detector_id = aws_guardduty_detector.guardduty_detector.id
  name        = "LAMBDA_NETWORK_LOGS"
  status      = "DISABLED"
}

resource "aws_guardduty_detector_feature" "runtime_monitoring_feature" {
  detector_id = aws_guardduty_detector.guardduty_detector.id
  name        = "RUNTIME_MONITORING"
  status      = "DISABLED"
}

resource "aws_guardduty_organization_configuration" "organization_config" {
  auto_enable_organization_members = "ALL"
  detector_id = aws_guardduty_detector.guardduty_detector.id
}

resource "aws_guardduty_organization_configuration_feature" "ec2_malware_scanning" {
  detector_id = aws_guardduty_detector.guardduty_detector.id
  name        = "EBS_MALWARE_PROTECTION"
  auto_enable = "ALL"
}
