output "guardduty_detector_id" {
  description = "The ID of the GuardDuty detector for the delegated admin account"
  value       = aws_guardduty_detector.guardduty_detector.id
}

output "organization_auto_enable_status" {
  description = "Whether GuardDuty is automatically enabled for all organization members"
  value       = aws_guardduty_organization_configuration.organization_config.auto_enable_organization_members
}

output "ec2_malware_scanning_status" {
  description = "The status of the EC2 malware scanning feature for the organization"
  value       = aws_guardduty_organization_configuration_feature.ec2_malware_scanning.auto_enable
}
