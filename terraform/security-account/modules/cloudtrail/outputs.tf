output "cloudtrail_id" {
  description = "ID of the CloudTrail trail"
  value       = aws_cloudtrail.org_trail.id
}

output "cloudtrail_arn" {
  description = "ARN of the CloudTrail trail"
  value       = aws_cloudtrail.org_trail.arn
}

output "cloudtrail_bucket_name" {
  description = "Name of the S3 bucket where CloudTrail logs are stored"
  value       = aws_s3_bucket.cloudtrail_logs.id
}

output "cloudtrail_bucket_arn" {
  description = "ARN of the S3 bucket where CloudTrail logs are stored"
  value       = aws_s3_bucket.cloudtrail_logs.arn
}

output "kms_key_id" {
  description = "ID of the KMS key used for CloudTrail log encryption"
  value       = aws_kms_key.cloudtrail_key.key_id
}

output "kms_key_arn" {
  description = "ARN of the KMS key used for CloudTrail log encryption"
  value       = aws_kms_key.cloudtrail_key.arn
}

output "cloudwatch_log_group_name" {
  description = "Name of the CloudWatch log group for CloudTrail logs"
  value       = aws_cloudwatch_log_group.cloudtrail_logs.name
}

output "cloudwatch_log_group_arn" {
  description = "ARN of the CloudWatch log group for CloudTrail logs"
  value       = aws_cloudwatch_log_group.cloudtrail_logs.arn
}
