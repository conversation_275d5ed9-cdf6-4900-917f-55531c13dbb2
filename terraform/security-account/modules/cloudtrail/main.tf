data "aws_caller_identity" "current" {}

# S3 bucket for CloudTrail logs
resource "aws_s3_bucket" "cloudtrail_logs" {
  bucket = var.cloudtrail_bucket_name
  force_destroy = var.s3_force_destroy

  object_lock_enabled = true
}

# S3 Cloudtrail logs bucket will be immutable
resource "aws_s3_bucket_object_lock_configuration" "cloudtrail_logs_lock" {
  bucket = aws_s3_bucket.cloudtrail_logs.id
  
  rule {
    default_retention {
      mode = "COMPLIANCE"
      days = var.s3_compliance_retention
    }
  }
}

# S3 bucket data will transition to Glacier
resource "aws_s3_bucket_lifecycle_configuration" "cloudtrail_lifecycle" {
  bucket = aws_s3_bucket.cloudtrail_logs.id
  
  rule {
    id = var.glacier_rule
    status = "Enabled"
    
    filter {
      prefix = "AWSLogs/"
      # filter by tags, however we need tags unification first
    }

    transition {
      days          = var.glacier_transition_days
      storage_class = "GLACIER"
    }
    
    noncurrent_version_transition {
      noncurrent_days = var.glacier_noncurrent_days
      storage_class   = "GLACIER"
    }
  }
}

# S3 bucket versioning
resource "aws_s3_bucket_versioning" "cloudtrail_logs_versioning" {
  bucket = aws_s3_bucket.cloudtrail_logs.id
  versioning_configuration {
    status = "Enabled"
  }
}

# S3 bucket policy for CloudTrail
resource "aws_s3_bucket_policy" "cloudtrail_logs_policy" {
  bucket = aws_s3_bucket.cloudtrail_logs.id
  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Sid    = "AWSCloudTrailAclCheck"
        Effect = "Allow"
        Principal = { Service = "cloudtrail.amazonaws.com" }
        Action = "s3:GetBucketAcl"
        Resource = aws_s3_bucket.cloudtrail_logs.arn
      },
      {
        Sid    = "AWSCloudTrailWrite"
        Effect = "Allow"
        Principal = { Service = "cloudtrail.amazonaws.com" }
        Action = "s3:PutObject"
        Resource = "${aws_s3_bucket.cloudtrail_logs.arn}/*"
        Condition = {
          StringEquals = {
            "s3:x-amz-acl" = "bucket-owner-full-control"
          }
        }
      },
      {
        Sid    = "LimitGetObject"
        Effect = "Allow"
        Principal = { AWS = var.cloudtrail_access_arns }
        Action = [
          "s3:GetObject",
          "s3:GetObjectVersion"
        ]
        Resource = "${aws_s3_bucket.cloudtrail_logs.arn}/*"
      }
    ]
  })
}

# Block public access
resource "aws_s3_bucket_public_access_block" "cloudtrail_logs_public_access" {
  bucket = aws_s3_bucket.cloudtrail_logs.id
  
  block_public_acls       = true
  block_public_policy     = true
  ignore_public_acls      = true
  restrict_public_buckets = true
}

# Server-side encryption
resource "aws_s3_bucket_server_side_encryption_configuration" "cloudtrail_logs_encryption" {
  bucket = aws_s3_bucket.cloudtrail_logs.id
  
  rule {
    apply_server_side_encryption_by_default {
      sse_algorithm     = "aws:kms"
      kms_master_key_id = aws_kms_key.cloudtrail_key.arn
    }
    # lower costs
    bucket_key_enabled = true
  }
}

# KMS key for CloudTrail
resource "aws_kms_key" "cloudtrail_key" {
  description         = "KMS key for CloudTrail logs"
  enable_key_rotation = true

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Sid    = "EnableIAMUserPermissions"
        Effect = "Allow"
        Principal = { AWS = var.cloudtrail_access_arns }
        Action   = "kms:*"
        Resource = "*"
      },
      {
        Sid    = "AllowCloudTrailToEncryptLogs"
        Effect = "Allow"
        Principal = { Service = "cloudtrail.amazonaws.com" }
        Action = [
          "kms:GenerateDataKey*",
          "kms:Decrypt",
          "kms:DescribeKey"
        ]
        Resource = "*"
      }
    ]
  })
}

# KMS key alias for better usability and key management
resource "aws_kms_alias" "cloudtrail_key_alias" {
  name          = var.kms_key_alias
  target_key_id = aws_kms_key.cloudtrail_key.key_id
}

# CloudWatch log group
resource "aws_cloudwatch_log_group" "cloudtrail_logs" {
  name = var.cloudwatch_log_group_name
  retention_in_days = var.cloudwatch_logs_retention_days
}

# IAM role for CloudTrail
resource "aws_iam_role" "cloudtrail_role" {
  name = var.cloudtrail_role_name
  
  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Principal = {
          Service = "cloudtrail.amazonaws.com"
        }
        Action = "sts:AssumeRole"
      }
    ]
  })
}

# CloudTrail role policy
resource "aws_iam_role_policy" "cloudtrail_policy" {
  name = var.cloudtrail_policy_name
  role = aws_iam_role.cloudtrail_role.id
  
  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "logs:CreateLogStream",
          "logs:PutLogEvents"
        ]
        Resource = "${aws_cloudwatch_log_group.cloudtrail_logs.arn}:*"
      }
    ]
  })
}

# CloudTrail configuration
resource "aws_cloudtrail" "org_trail" {
  name                          = var.cloudtrail_name
  s3_bucket_name                = aws_s3_bucket.cloudtrail_logs.id
  is_organization_trail         = var.is_organization_trail
  is_multi_region_trail         = var.is_multi_region_trail
  include_global_service_events = var.include_global_service_events
  enable_log_file_validation    = var.enable_log_file_validation
  kms_key_id                    = aws_kms_key.cloudtrail_key.arn
  cloud_watch_logs_group_arn    = "${aws_cloudwatch_log_group.cloudtrail_logs.arn}:*"
  cloud_watch_logs_role_arn     = aws_iam_role.cloudtrail_role.arn
  
  # Basic management events configuration
  # This is extended by the cloudtrail-advanced-event-selectors.py script
  event_selector {
    read_write_type           = "All"
    include_management_events = true
    exclude_management_event_sources = [
      "kms.amazonaws.com",
      "rdsdata.amazonaws.com"
    ]
  }
  
  depends_on = [
    aws_s3_bucket_policy.cloudtrail_logs_policy
  ]

  # Enable Insights
  insight_selector {
    insight_type = "ApiCallRateInsight"
  }
  
  insight_selector {
    insight_type = "ApiErrorRateInsight"
  }
}
