variable "account_id" {
  description = "AWS Account ID"
  type        = string
}

variable "cloudtrail_name" {
  description = "Name of the CloudTrail trail"
  type        = string
}

variable "cloudtrail_bucket_name" {
  description = "Name of the S3 bucket for CloudTrail logs"
  type        = string
}

variable "s3_force_destroy" {
  description = "Whether to force destroy the S3 bucket even if it contains objects"
  type        = bool
  default     = false
}

variable "cloudwatch_logs_retention_days" {
  description = "Number of days to retain CloudWatch logs"
  type        = number
}

variable "cloudtrail_access_arns" {
  description = "List of ARNs for IAM users or roles that are granted access to manage CloudTrail-related resources, including the S3 bucket and KMS key."
  type        = list(string)
}

variable "kms_key_alias" {
  description = "Alias for the KMS key"
  type        = string
}

variable "cloudwatch_log_group_name" {
  description = "Name of CloudWatch log group for CloudTrail logs"
  type        = string
}

variable "cloudtrail_role_name" {
  description = "Name of IAM role for CloudTrail"
  type        = string
}

variable "cloudtrail_policy_name" {
  description = "Name of IAM policy for CloudTrail"
  type        = string
}

variable "is_organization_trail" {
  description = "Specifies whether the trail is an AWS Organizations trail"
  type        = bool
  default     = true
}

variable "is_multi_region_trail" {
  description = "Specifies whether the trail is created in all regions"
  type        = bool
  default     = true
}

variable "include_global_service_events" {
  description = "Specifies whether the trail includes global service events"
  type        = bool
  default     = true
}

variable "enable_log_file_validation" {
  description = "Specifies whether log file validation is enabled"
  type        = bool
  default     = true
}

variable "glacier_transition_days" {
  description = "Number of days after object creation before transitioning to Glacier storage class"
  type        = number
}

variable "glacier_noncurrent_days" {
  description = "Number of days after an object version becomes noncurrent before transitioning it to Glacier storage class"
  type        = number
}

variable "s3_compliance_retention" {
  description = "Number of days to retain objects when using S3 Object Lock in COMPLIANCE mode, making them immutable for this period"
  type        = number
}

variable "glacier_rule" {
  description = "Identifier name for the S3 lifecycle rule that manages transitions to Glacier storage"
  type        = string
}
