# Create a Finding Aggregator for centralized findings
resource "aws_securityhub_finding_aggregator" "finding_aggregator" {
  linking_mode = "ALL_REGIONS"
}

# Enable Security Hub aggregation from all organization accounts
# StackSet with AWS Config setup needs to be deployed afterwards
# /aws-iac/cloudformation/security-account/config-configuration-recorder-sets
resource "aws_securityhub_organization_configuration" "organization_config" {
  auto_enable         = false
  auto_enable_standards = "NONE"
  organization_configuration {
    configuration_type = "CENTRAL"
  }

  depends_on = [aws_securityhub_finding_aggregator.finding_aggregator]
}


resource "aws_securityhub_configuration_policy" "org_cis_benchmark" {
  name        = "OrganizationCISPolicy"
  description = "Enables CIS AWS Foundations Benchmark v3.0.0 for the organization"

  configuration_policy {
    service_enabled = true
    enabled_standard_arns = [
      # Seems like a good baseline
      "arn:aws:securityhub:eu-west-1::standards/cis-aws-foundations-benchmark/v/3.0.0",
      "arn:aws:securityhub:eu-west-1::standards/pci-dss/v/3.2.1"
    ]
    security_controls_configuration {
      disabled_control_identifiers = [
        "CloudTrail.1", # CloudTrail should be enabled and configured with at least one multi-Region trail that includes read and write management events
                        # Note: CloudTrail is enabled, however we're exlcuding KMS and RDS API calls, thus it would fail
        "CloudTrail.7"  # Ensure S3 bucket access logging is enabled on the CloudTrail S3 bucket
                        # Note: No need, we can setup alerting around that
      ]
    }
  }

  depends_on = [aws_securityhub_organization_configuration.organization_config]
}

# Run configuration policy against organization
resource "aws_securityhub_configuration_policy_association" "org_root" {
  target_id = "r-8ja1"
  policy_id = aws_securityhub_configuration_policy.org_cis_benchmark.id
}

# IAM Access Analyzer integration
resource "aws_securityhub_product_subscription" "iam_access_analyzer_integration" {
  product_arn = "arn:aws:securityhub:eu-west-1::product/aws/access-analyzer"
}

# GuardDuty integration
resource "aws_securityhub_product_subscription" "guardduty_integration" {
  product_arn = "arn:aws:securityhub:eu-west-1::product/aws/guardduty"
}

# AWS Config integration
resource "aws_securityhub_product_subscription" "config_integration" {
  product_arn = "arn:aws:securityhub:eu-west-1::product/aws/config"
}

# Inspector integration
resource "aws_securityhub_product_subscription" "inspector_integration" {
  product_arn = "arn:aws:securityhub:eu-west-1::product/aws/inspector"
}

# Health integration
resource "aws_securityhub_product_subscription" "health_integration" {
  product_arn = "arn:aws:securityhub:eu-west-1::product/aws/health"
}
