variable "role_name" {
  description = "Name of the IAM role for Terraform deployments"
  type        = string
  default     = "security-terraform-deployer"
}

variable "account_id" {
  description = "AWS account ID where the role will be created"
  type        = string
}

variable "allowed_principal_arns" {
  description = "List of AWS principal ARNs allowed to assume this role"
  type        = list(string)
  default     = []
}

variable "policy_name_prefix" {
  description = "Prefix for IAM policy names"
  type        = string
  default     = "security-terraform-deployer"
}


variable "tags" {
  description = "Tags to apply to resources"
  type        = map(string)
  default     = {}
}

variable "templates_bucket_name" {
  description = "Name of the S3 bucket for CloudFormation templates"
  type        = string
}

variable "security_reports_bucket_name" {
  description = "Name of the S3 bucket for security reports"
  type        = string
}
