resource "aws_iam_role" "security_terraform_deployer" {
  name               = var.role_name
  description        = "Role for deploying security resources with Terraform"
  assume_role_policy = data.aws_iam_policy_document.trust_policy.json

  tags = merge(
    var.tags,
    {
      Name        = var.role_name
      Description = "Security Terraform deployer role"
    }
  )
}

data "aws_iam_policy_document" "trust_policy" {
  statement {
    effect = "Allow"
    actions = ["sts:AssumeRole"]
    principals {
      type        = "AWS"
      identifiers = var.allowed_principal_arns
    }
  }
}

# Add S3 permissions for CloudFormation template deployment
data "aws_iam_policy_document" "s3_templates_policy" {
  statement {
    effect = "Allow"
    actions = [
      "s3:DeleteBucket",
      "s3:PutBucketPolicy",
      "s3:PutObject",
      "s3:PutObjectAcl",
      "s3:PutBucketPublicAccessBlock",
      "s3:PutEncryptionConfiguration",
      "s3:PutBucketVersioning",
      "s3:GetObject",
      "s3:CreateBucket",
      "s3:ListBucket",
      "s3:Get*"
    ]
    resources = [
      "arn:aws:s3:::${var.templates_bucket_name}",
      "arn:aws:s3:::${var.templates_bucket_name}/*",
      "arn:aws:s3:::${var.security_reports_bucket_name}",
      "arn:aws:s3:::${var.security_reports_bucket_name}/*",
    ]
  }

  # Additional permission to list all buckets (read-only)
  statement {
    effect = "Allow"
    actions = [
      "s3:ListAllMyBuckets",
      "s3:GetBucketLocation"
    ]
    resources = ["*"]
  }
}

resource "aws_iam_policy" "s3_templates_policy" {
  name        = "${var.policy_name_prefix}-s3-templates"
  description = "Policy for managing CloudFormation templates in S3"
  policy      = data.aws_iam_policy_document.s3_templates_policy.json
}

resource "aws_iam_role_policy_attachment" "s3_templates" {
  role       = aws_iam_role.security_terraform_deployer.name
  policy_arn = aws_iam_policy.s3_templates_policy.arn
}
