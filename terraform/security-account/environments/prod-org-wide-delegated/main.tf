provider "aws" {
  region  = "eu-west-1"
  profile = var.aws_profile
}

data "aws_caller_identity" "current" {}

locals {
  admin_access_arns = [
    "arn:aws:iam::************:root",
    "arn:aws:iam::************:role/aws-reserved/sso.amazonaws.com/eu-west-1/AWSReservedSSO_ProdAdministratorAccess_6df808a230b1a416"
  ]
}

module "terraform_state_backend" {
  source = "../../../shared_modules/terraform_backend"
  state_bucket_name = "aws-iac-terraform-state-aent-security-prod"
}

// CLOUDTRAIL
module "cloudtrail" {
  source = "../../modules/cloudtrail"

  account_id                        = "************"
  cloudtrail_name                   = "cloudtrail-org-wide"
  cloudtrail_bucket_name            = "cloudtrail-org-wide"
  s3_force_destroy                  = false
  cloudwatch_logs_retention_days    = 7
  glacier_transition_days           = 90
  glacier_noncurrent_days           = 30
  s3_compliance_retention           = 90
  cloudtrail_access_arns = local.admin_access_arns

  # Customize resource names
  kms_key_alias             = "alias/cloudtrail-org-wide-key"
  cloudwatch_log_group_name = "cloudtrail-org-wide-logs"
  cloudtrail_role_name      = "cloudtrail-service-role"
  cloudtrail_policy_name    = "cloudtrail-logs-policy"
  glacier_rule              = "cloudtrail-org-wide-archive"

  # Features
  is_organization_trail           = true
  is_multi_region_trail           = true
  include_global_service_events   = true
  enable_log_file_validation      = true
}

// IAM
module "security-terraform-deployer-role" {
  source = "../../modules/iam/security-terraform-deployer-role"
  account_id = ************
  allowed_principal_arns = local.admin_access_arns
  templates_bucket_name = "cf-templates-${data.aws_caller_identity.current.account_id}"
  security_reports_bucket_name = "security-reports-${data.aws_caller_identity.current.account_id}"
}

// AWS CONFIG
module "aws_config" {
  source = "../../modules/config"

  config_logs_bucket_name = "config-org-wide-logs"
  config_aggregator_role = "config-org-aggregator-role"
  config_aggregator_name = "config-org-aggregator"
  config_access_arns = local.admin_access_arns
  organization_id = "o-mva599s896"
}

// SECURITY HUB
module "securityhub" {
  source = "../../modules/securityhub"
}

// GUARD DUTY
module "guardduty_eu_west_1" {
  source = "../../modules/guardduty"
  providers = { aws = aws.eu-west-1 }
}

module "guardduty_eu_west_2" {
  source = "../../modules/guardduty"
  providers = { aws = aws.eu-west-2 }
}

module "guardduty_eu_west_3" {
  source = "../../modules/guardduty"
  providers = { aws = aws.eu-west-3 }
}

module "guardduty_eu_central_1" {
  source = "../../modules/guardduty"
  providers = { aws = aws.eu-central-1 }
}

module "guardduty_eu_north_1" {
  source = "../../modules/guardduty"
  providers = { aws = aws.eu-north-1 }
}

module "guardduty_us_west_1" {
  source = "../../modules/guardduty"
  providers = { aws = aws.us-west-1 }
}

module "guardduty_us_west_2" {
  source = "../../modules/guardduty"
  providers = { aws = aws.us-west-2 }
}

module "guardduty_us_east_1" {
  source = "../../modules/guardduty"
  providers = { aws = aws.us-east-1 }
}

module "guardduty_us_east_2" {
  source = "../../modules/guardduty"
  providers = { aws = aws.us-east-2 }
}

module "guardduty_ap_south_1" {
  source = "../../modules/guardduty"
  providers = { aws = aws.ap-south-1 }
}

module "guardduty_ap_northeast_1" {
  source = "../../modules/guardduty"
  providers = { aws = aws.ap-northeast-1 }
}

module "guardduty_ap_northeast_2" {
  source = "../../modules/guardduty"
  providers = { aws = aws.ap-northeast-2 }
}

module "guardduty_ap_northeast_3" {
  source = "../../modules/guardduty"
  providers = { aws = aws.ap-northeast-3 }
}

module "guardduty_ap_southeast_1" {
  source = "../../modules/guardduty"
  providers = { aws = aws.ap-southeast-1 }
}

module "guardduty_ap_southeast_2" {
  source = "../../modules/guardduty"
  providers = { aws = aws.ap-southeast-2 }
}

module "guardduty_ca_central_1" {
  source = "../../modules/guardduty"
  providers = { aws = aws.ca-central-1 }
}

module "guardduty_sa_east_1" {
  source = "../../modules/guardduty"
  providers = { aws = aws.sa-east-1 }
}

// IAM ACCESS ANALYZER
module "iam_access_analyzer_eu_west_1" {
  source        = "../../modules/iam_access_analyzer"
  analyzer_name = "access-analyzer-${data.aws_caller_identity.current.account_id}-eu-west-1"
  providers     = { aws = aws.eu-west-1 }
}

module "iam_access_analyzer_eu_west_2" {
  source        = "../../modules/iam_access_analyzer"
  analyzer_name = "access-analyzer-${data.aws_caller_identity.current.account_id}-eu-west-2"
  providers     = { aws = aws.eu-west-2 }
}

module "iam_access_analyzer_eu_west_3" {
  source        = "../../modules/iam_access_analyzer"
  analyzer_name = "access-analyzer-${data.aws_caller_identity.current.account_id}-eu-west-3"
  providers     = { aws = aws.eu-west-3 }
}

module "iam_access_analyzer_eu_central_1" {
  source        = "../../modules/iam_access_analyzer"
  analyzer_name = "access-analyzer-${data.aws_caller_identity.current.account_id}-eu-central-1"
  providers     = { aws = aws.eu-central-1 }
}

module "iam_access_analyzer_eu_north_1" {
  source        = "../../modules/iam_access_analyzer"
  analyzer_name = "access-analyzer-${data.aws_caller_identity.current.account_id}-eu-north-1"
  providers     = { aws = aws.eu-north-1 }
}

module "iam_access_analyzer_us_west_1" {
  source        = "../../modules/iam_access_analyzer"
  analyzer_name = "access-analyzer-${data.aws_caller_identity.current.account_id}-us-west-1"
  providers     = { aws = aws.us-west-1 }
}

module "iam_access_analyzer_us_west_2" {
  source        = "../../modules/iam_access_analyzer"
  analyzer_name = "access-analyzer-${data.aws_caller_identity.current.account_id}-us-west-2"
  providers     = { aws = aws.us-west-2 }
}

module "iam_access_analyzer_us_east_1" {
  source        = "../../modules/iam_access_analyzer"
  analyzer_name = "access-analyzer-${data.aws_caller_identity.current.account_id}-us-east-1"
  providers     = { aws = aws.us-east-1 }
}

module "iam_access_analyzer_us_east_2" {
  source        = "../../modules/iam_access_analyzer"
  analyzer_name = "access-analyzer-${data.aws_caller_identity.current.account_id}-us-east-2"
  providers     = { aws = aws.us-east-2 }
}

module "iam_access_analyzer_ap_south_1" {
  source        = "../../modules/iam_access_analyzer"
  analyzer_name = "access-analyzer-${data.aws_caller_identity.current.account_id}-ap-south-1"
  providers     = { aws = aws.ap-south-1 }
}

module "iam_access_analyzer_ap_northeast_1" {
  source        = "../../modules/iam_access_analyzer"
  analyzer_name = "access-analyzer-${data.aws_caller_identity.current.account_id}-ap-northeast-1"
  providers     = { aws = aws.ap-northeast-1 }
}

module "iam_access_analyzer_ap_northeast_2" {
  source        = "../../modules/iam_access_analyzer"
  analyzer_name = "access-analyzer-${data.aws_caller_identity.current.account_id}-ap-northeast-2"
  providers     = { aws = aws.ap-northeast-2 }
}

module "iam_access_analyzer_ap_northeast_3" {
  source        = "../../modules/iam_access_analyzer"
  analyzer_name = "access-analyzer-${data.aws_caller_identity.current.account_id}-ap-northeast-3"
  providers     = { aws = aws.ap-northeast-3 }
}

module "iam_access_analyzer_ap_southeast_1" {
  source        = "../../modules/iam_access_analyzer"
  analyzer_name = "access-analyzer-${data.aws_caller_identity.current.account_id}-ap-southeast-1"
  providers     = { aws = aws.ap-southeast-1 }
}

module "iam_access_analyzer_ap_southeast_2" {
  source        = "../../modules/iam_access_analyzer"
  analyzer_name = "access-analyzer-${data.aws_caller_identity.current.account_id}-ap-southeast-2"
  providers     = { aws = aws.ap-southeast-2 }
}

module "iam_access_analyzer_ca_central_1" {
  source        = "../../modules/iam_access_analyzer"
  analyzer_name = "access-analyzer-${data.aws_caller_identity.current.account_id}-ca-central-1"
  providers     = { aws = aws.ca-central-1 }
}

module "iam_access_analyzer_sa_east_1" {
  source        = "../../modules/iam_access_analyzer"
  analyzer_name = "access-analyzer-${data.aws_caller_identity.current.account_id}-sa-east-1"
  providers     = { aws = aws.sa-east-1 }
}
