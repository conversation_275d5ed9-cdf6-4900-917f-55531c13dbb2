provider "aws" {
  region = "eu-west-1"
  profile = "security-dev"

  assume_role {
    role_arn     = "arn:aws:iam::************:role/security-terraform-deployer"
    session_name = "security-terraform-deployer"
  }
}

data "aws_caller_identity" "current" {}

module "cf_templates_bucket" {
  source      = "../../modules/s3/cloudformation-templates"
  bucket_name = "cf-templates-${data.aws_caller_identity.current.account_id}"
  organization_id = "o-mva599s896"
}

module "security_reports_bucket" {
  source      = "../../modules/s3/security_reports"
  bucket_name = "security-reports-${data.aws_caller_identity.current.account_id}"
  organization_id = "o-mva599s896"
}

