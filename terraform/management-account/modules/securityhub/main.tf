# Security Hub account configuration
resource "aws_securityhub_account" "securityhub_account" {
  enable_default_standards = false
  auto_enable_controls = true
  control_finding_generator = "SECURITY_CONTROL"
}

# Delegate admin account for Security Hub
resource "aws_securityhub_organization_admin_account" "securityhub_admin" {
  admin_account_id = var.delegeted_admin_account_id
  depends_on       = [aws_securityhub_account.securityhub_account]
}
