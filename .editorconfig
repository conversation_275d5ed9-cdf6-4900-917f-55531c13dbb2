# EditorConfig helps developers define and maintain consistent
# coding styles between different editors and IDEs
# editorconfig.org

root = true

[*]
end_of_line = lf
insert_final_newline = true
charset = utf-8
trim_trailing_whitespace = true
insert_final_newline = true
indent_style = space
indent_size = 4
max_line_length = 120

[*.md]
# double whitespace at end of line
# denotes a line break in Markdown
trim_trailing_whitespace = false

[{*.yml,*.yaml}]
indent_size = 2

[*.tf]
indent_size = 2

[{Makefile,**.mk}]
# Use tabs for indentation (Makefiles require tabs)
indent_style = tab