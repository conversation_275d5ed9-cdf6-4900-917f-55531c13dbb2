# AWS IAC

Code for storing configuration of our AWS accounts.

## Getting Started

### Configure AWS SSO

```bash
aws configure sso
```

Use the following settings:
- **Session name:** `aent`
- **Start URL:** `https://metawin.awsapps.com/start/#`
- **Region:** `eu-west-1`
- **Scope:** Leave blank

## Development

### Makefile Tips

If you encounter a "missing separator" error, run the following command to replace spaces with tabs:

```bash
sed -i 's/^    /\t/' Makefile
```

### Required Linting Tools

Install the following linting tools to validate CloudFormation templates:

| Tool | Installation | Type |
|------|-------------|------|
| cfn-lint | `pip install cfn-lint` | Python |
| cfn_nag | `gem install cfn-nag` | Ruby |
