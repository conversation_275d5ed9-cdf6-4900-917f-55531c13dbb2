AWSTemplateFormatVersion: "2010-09-09"
Description: |
  Template to deploy an AWS Config Recorder and Delivery Channel in a member account.

Conditions:
  IsPrimaryRegion: !Equals [!Ref "AWS::Region", "eu-west-1"]

Resources:
  ConfigServiceLinkedRole:
    Type: AWS::IAM::ServiceLinkedRole
    Condition: IsPrimaryRegion
    Properties:
      AWSServiceName: config.amazonaws.com

  ConfigRecorder:
    Type: AWS::Config::ConfigurationRecorder
    Properties:
      Name: default
      RoleARN: !Sub "arn:aws:iam::${AWS::AccountId}:role/aws-service-role/config.amazonaws.com/AWSServiceRoleForConfig"
      RecordingGroup:
        AllSupported: true
        IncludeGlobalResourceTypes: !If [IsPrimaryRegion, true, false]
      RecordingMode: !If
        - IsPrimaryRegion
        - # Primary region config with global resources
          RecordingFrequency: DAILY
          RecordingModeOverrides:
            - RecordingFrequency: CONTINUOUS
              ResourceTypes:
                - AWS::IAM::User
                - AWS::IAM::Role
                - AWS::IAM::Policy
                - AWS::IAM::Group
                - AWS::EC2::Instance
                - AWS::EC2::SecurityGroup
                - AWS::EC2::NetworkAcl
                - AWS::KMS::Key
                - AWS::S3::Bucket
        - # Non-primary region config without global resources
          RecordingFrequency: DAILY
          RecordingModeOverrides:
            - RecordingFrequency: CONTINUOUS
              ResourceTypes:
                - AWS::EC2::Instance
                - AWS::EC2::SecurityGroup
                - AWS::EC2::NetworkAcl
                - AWS::KMS::Key
                - AWS::S3::Bucket

  DeliveryChannel:
    Type: AWS::Config::DeliveryChannel
    Properties:
      Name: default
      S3BucketName: config-org-wide-logs
      S3KmsKeyArn: arn:aws:kms:eu-west-1:************:key/9905c17c-c754-4b80-9526-84e6ad864692
      ConfigSnapshotDeliveryProperties:
        DeliveryFrequency: TwentyFour_Hours

Outputs:
  ConfigRecorderName:
    Description: The name of the created Config Recorder
    Value: !Ref ConfigRecorder
  DeliveryChannelName:
    Description: The name of the created Delivery Channel
    Value: !Ref DeliveryChannel
  ConfigServiceLinkedRoleArn:
    Description: The ARN of the AWS Config Service Linked Role
    Value: !Sub "arn:aws:iam::${AWS::AccountId}:role/aws-service-role/config.amazonaws.com/AWSServiceRoleForConfig"
