---
AWSTemplateFormatVersion: "2010-09-09"
Transform: 'AWS::LanguageExtensions'
Description: |
  CloudFormation template to deploy AWS Config Recorders and
  Delivery Channels across multiple accounts and regions using a StackSet.
Parameters:
  OrgUnitIds:
    Description: List of Organizational Unit IDs to target for StackSet deployment.
    Type: CommaDelimitedList
    Default: "r-8ja1"
  InstanceTemplateUrl:
    Description: S3 URL for the stack instance template
    Type: String

Resources:
  ConfigRecorderStackSet:
    Type: AWS::CloudFormation::StackSet
    Properties:
      StackSetName: org-config-recorders
      Description: |
        StackSet to deploy AWS Config Recorders and Delivery Channels to
        specified regions.
      PermissionModel: SERVICE_MANAGED
      CallAs: DELEGATED_ADMIN
      AutoDeployment:
        Enabled: true
        RetainStacksOnAccountRemoval: false
      StackInstancesGroup:
        - DeploymentTargets:
            OrganizationalUnitIds: !Ref OrgUnitIds
          Regions:
            - eu-north-1
            - eu-west-1
            - eu-west-2
            - eu-west-3
            - eu-central-1
            - ap-south-1
            - ap-northeast-1
            - ap-northeast-2
            - ap-northeast-3
            - ap-southeast-1
            - ap-southeast-2
            - us-east-1
            - us-east-2
            - us-west-1
            - us-west-2
            - ca-central-1
            - sa-east-1
      TemplateURL: !Ref InstanceTemplateUrl
