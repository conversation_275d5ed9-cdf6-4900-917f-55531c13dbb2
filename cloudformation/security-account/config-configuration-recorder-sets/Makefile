.PHONY: default clean create update

default: .lint .nag

.lint:
	cfn-lint cloudformation.yaml

.nag:
	cfn_nag --fail-on-warnings cloudformation.yaml

AWS_PROFILE ?= $(error Please set the AWS_PROFILE environment variable)
STACK_NAME ?= config-recorder-stack
TEMPLATE_URL ?= https://cf-templates-629235567336.s3.amazonaws.com/config-recorder/stack-instance-template.yaml

update-template:
	@echo "Uploading template to S3"
	aws s3 cp ./stack-instance-template.yaml s3://cf-templates-629235567336/config-recorder/ --profile ${AWS_PROFILE}

create: update-template
	@echo "Creating CloudFormation stack"
	aws cloudformation create-stack \
		--profile ${AWS_PROFILE} \
		--region eu-west-1 \
		--stack-name ${STACK_NAME} \
		--template-body file://./cloudformation.yaml \
		--capabilities CAPABILITY_NAMED_IAM CAPABILITY_AUTO_EXPAND \
		--parameters ParameterKey=InstanceTemplateUrl,ParameterValue=${TEMPLATE_URL}

update: update-template
	@echo "Updating CloudFormation stack"
	aws cloudformation update-stack \
		--profile ${AWS_PROFILE} \
		--region eu-west-1 \
		--stack-name ${STACK_NAME} \
		--template-body file://./cloudformation.yaml \
		--capabilities CAPABILITY_NAMED_IAM CAPABILITY_AUTO_EXPAND \
		--parameters ParameterKey=InstanceTemplateUrl,ParameterValue=${TEMPLATE_URL}
