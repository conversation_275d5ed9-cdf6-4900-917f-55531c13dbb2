#!/bin/bash

# The script can be used to clear AWS Config if CloudFormation stack glitches
# and can't remove the resources

if [ -z "$1" ] || [[ "$1" != "--profile" ]]; then
    echo "Error: AWS profile is required"
    echo "Usage: $0 --profile <aws-profile>"
    exit 1
fi
if [ -z "$2" ]; then
    echo "Error: Profile value missing"
    echo "Usage: $0 --profile <aws-profile>"
    exit 1
fi
PROFILE=$2

REGIONS=(
  us-east-1 us-east-2 us-west-1 us-west-2
  ca-central-1
  eu-west-1 eu-west-2 eu-west-3 eu-north-1 eu-central-1
  ap-south-1 ap-southeast-1 ap-southeast-2
  ap-northeast-1 ap-northeast-2 ap-northeast-3
  sa-east-1
)

for region in "${REGIONS[@]}"; do
    aws configservice stop-configuration-recorder --configuration-recorder-name default --region $region --profile $PROFILE
    aws configservice delete-configuration-recorder --configuration-recorder-name default --region $region --profile $PROFILE
    aws configservice delete-delivery-channel --delivery-channel-name default --region $region --profile $PROFILE
done

aws iam delete-service-linked-role --role-name AWSServiceRoleForConfig --profile $PROFILE
