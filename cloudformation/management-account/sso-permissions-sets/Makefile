.PHONY: default clean create update

default: .lint .nag

.lint: root.yaml
	cfn-lint cloudformation.yaml
	touch .lint

.nag: terraform-state-backend.yaml
	cfn_nag --fail-on-warnings --blacklist-path ignorelist.yaml cloudformation.yaml
	touch .nag

clean:
	rm .lint .nag

AWS_PROFILE ?= $(error Please set the AWS_PROFILE environment variable)
STACK_NAME ?= management-account-stack
create:
	aws cloudformation create-stack \
		--profile ${AWS_PROFILE} \
		--region eu-west-1 \
		--stack-name ${STACK_NAME} \
		--template-body file://./cloudformation.yaml \
		--capabilities CAPABILITY_NAMED_IAM CAPABILITY_AUTO_EXPAND

update:
	aws cloudformation update-stack \
		--profile ${AWS_PROFILE} \
		--region eu-west-1 \
		--stack-name ${STACK_NAME} \
		--template-body file://./cloudformation.yaml \
		--capabilities CAPABILITY_NAMED_IAM CAPABILITY_AUTO_EXPAND
