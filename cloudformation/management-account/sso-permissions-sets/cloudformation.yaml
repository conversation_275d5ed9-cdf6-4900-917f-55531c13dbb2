---
AWSTemplateFormatVersion: "2010-09-09"
Transform: 'AWS::LanguageExtensions'
Description: |
  This Cloudformation template creates the permissions sets
  and assign groups to them to provide access to our resources
Parameters:
  AdminGroupID:
      Description: Admin Group to assign the permission set to
      Type: String
      Default: 92f5b494-d021-709f-d63c-e56e54ca2cb4
  DevAccountIDs:
      Description: List of all developer account IDs
      Type: CommaDelimitedList
      Default: "************,************,************,************,************,************,************,************,************,************,************,************,************,************,************,************,************,************,************,************"
  EngGroupID:
      Description: Engineering Group to assign the permission set to
      Type: String
      Default: a285e424-40c1-7099-4a00-4c228bb597bf
  ProdAccountIDs:
      Description: List of all prod account IDs
      Type: CommaDelimitedList
      Default: "************,************,************,************,************,************,************,************,************,************,************,************,************,************,************"
  OpsAccountIDs:
      Description: List of all ops account IDs
      Type: CommaDelimitedList
      Default: "************,************"
  OpsGroupID:
      Description: Ops Group to assign the permission set to
      Type: String
      Default: 1205d4c4-00e1-709a-d89b-0281f9ecdca6
  OrgAdminGroupID:
      Description: Org Admin Group to assign the permission set to
      Type: String
      Default: 22f554c4-80b1-7045-ba01-6e63358021e9
  SSOArn:
    Description: Arn for SSO Instance
    Type: String
    Default: arn:aws:sso:::instance/ssoins-6804d82492e4a99a
Resources:
  DevAdminPermissionSet:
    Properties:
      InstanceArn: !Ref SSOArn
      Name: DevAdministratorAccess
      ManagedPolicies:
        - arn:aws:iam::aws:policy/AdministratorAccess
    Type: AWS::SSO::PermissionSet
  'Fn::ForEach::DevAdminAssignments':
    - AccountID
    - !Ref DevAccountIDs
    - 'DevAdminAssignment${AccountID}':
        Properties:
          InstanceArn: !Ref SSOArn
          PermissionSetArn: !GetAtt DevAdminPermissionSet.PermissionSetArn
          PrincipalType: GROUP
          PrincipalId: !Ref EngGroupID
          TargetId: !Ref AccountID
          TargetType: AWS_ACCOUNT
        Type: AWS::SSO::Assignment
  OpsUserPermissionSet:
    Properties:
      InstanceArn: !Ref SSOArn
      Name: OpsUserAccess
      InlinePolicy:
        Version: '2012-10-17'
        Statement:
          - Action:
              - cloudfront:CreateInvalidation
              - cloudfront:GetDistribution
              - cloudfront:ListDistributions
            Effect: Allow
            Resource: "*"
          - Action:
              - s3:*
            Effect: Allow
            Resource:
              - arn:aws:s3:::origin.content.prod.platform.metawin.com
              - arn:aws:s3:::origin.content.prod.platform.metawin.com/*
              - arn:aws:s3:::origin.content.prod.platform.metawin.us
              - arn:aws:s3:::origin.content.prod.platform.metawin.us/*
              - arn:aws:s3:::com.metawin.prod.data
              - arn:aws:s3:::com.metawin.prod.data/*
              - arn:aws:s3:::com.metawin-us.prod.data
              - arn:aws:s3:::com.metawin-us.prod.data/*
          - Action:
              - s3:ListAllMyBuckets
              - ses:*
              - mobiletargeting:*
            Effect: Allow
            Resource: "*"
    Type: AWS::SSO::PermissionSet
  'Fn::ForEach::OpsUserAssignments':
    - AccountID
    - !Ref OpsAccountIDs
    - 'OpsUserAssignment${AccountID}':
        Properties:
          InstanceArn: !Ref SSOArn
          PermissionSetArn: !GetAtt OpsUserPermissionSet.PermissionSetArn
          PrincipalType: GROUP
          PrincipalId: !Ref OpsGroupID
          TargetId: !Ref AccountID
          TargetType: AWS_ACCOUNT
        Type: AWS::SSO::Assignment
  OrgAdminPermissionSet:
    Properties:
      InstanceArn: !Ref SSOArn
      Name: OrgAdministratorAccess
      ManagedPolicies:
        - arn:aws:iam::aws:policy/AdministratorAccess
    Type: AWS::SSO::PermissionSet
  OrgAdminAssignment:
    Properties:
      InstanceArn: !Ref SSOArn
      PermissionSetArn: !GetAtt OrgAdminPermissionSet.PermissionSetArn
      PrincipalType: GROUP
      PrincipalId: !Ref OrgAdminGroupID
      TargetId: !Ref AWS::AccountId
      TargetType: AWS_ACCOUNT
    Type: AWS::SSO::Assignment
  ProdAdminPermissionSet:
    Properties:
      InstanceArn: !Ref SSOArn
      Name: ProdAdministratorAccess
      ManagedPolicies:
        - arn:aws:iam::aws:policy/AdministratorAccess
    Type: AWS::SSO::PermissionSet
  'Fn::ForEach::ProdAdminAssignments':
    - AccountID
    - !Ref ProdAccountIDs
    - 'ProdAdminAssignment${AccountID}':
        Properties:
          InstanceArn: !Ref SSOArn
          PermissionSetArn: !GetAtt ProdAdminPermissionSet.PermissionSetArn
          PrincipalType: GROUP
          PrincipalId: !Ref AdminGroupID
          TargetId: !Ref AccountID
          TargetType: AWS_ACCOUNT
        Type: AWS::SSO::Assignment
  ProdReadOnlyPermissionSet:
    Properties:
      InstanceArn: !Ref SSOArn
      Name: ProdReadOnlyAccess
      ManagedPolicies:
        - arn:aws:iam::aws:policy/CloudWatchReadOnlyAccess
    Type: AWS::SSO::PermissionSet
  'Fn::ForEach::ProdReadOnlyAssignments':
    - AccountID
    - !Ref ProdAccountIDs
    - 'ProdReadOnlyAssignment${AccountID}':
        Properties:
          InstanceArn: !Ref SSOArn
          PermissionSetArn: !GetAtt ProdReadOnlyPermissionSet.PermissionSetArn
          PrincipalType: GROUP
          PrincipalId: !Ref EngGroupID
          TargetId: !Ref AccountID
          TargetType: AWS_ACCOUNT
        Type: AWS::SSO::Assignment
