---
AWSTemplateFormatVersion: "2010-09-09"
Transform: 'AWS::LanguageExtensions'
Description: |
  This Cloudformation template creates the permissions
  to allow the watchtowr integration
Parameters:
  externalID:
    Type: String
    Description: watchTowr external ID
Resources:
  WatchtowrAWSConnectorPolicy:
    Properties:
      PolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Action:
              - iam:ListAccountAliases
              - organizations:ListAccounts
            Resource: '*'
          - Effect: Allow
            Action:
              - sts:AssumeRole
            Resource: 'arn:aws:iam::*:role/watchTowrAWSConnectorRole'
      ManagedPolicyName: watchTowrManagementAccountAccess
    Type: AWS::IAM::ManagedPolicy
  WatchtowrAWSConnectorRole:
    Properties:
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              AWS: ************
            Action: sts:AssumeRole
            Condition:
              StringEquals:
                sts:ExternalId: !Ref externalID
      ManagedPolicyArns:
        - !Ref WatchtowrAWSConnectorPolicy
      RoleName: watchTowrIntegrationRole
    Type: AWS::IAM::Role
