.PHONY: default clean create update create-stackset create-stackset-instances

default: .lint .nag

.lint: root.yaml
	cfn-lint cloudformation.yaml
	touch .lint

.nag: terraform-state-backend.yaml
	cfn_nag --fail-on-warnings --blacklist-path ignorelist.yaml cloudformation.yaml
	touch .nag

clean:
	rm .lint .nag

AWS_PROFILE ?= $(error Please set the AWS_PROFILE environment variable)
STACK_NAME ?= watchtowr-stack
EXTERNAL_ID ?= 5635B641-47FD-4EE4-BBC7-8EDD7077546B
create:
	aws cloudformation create-stack \
		--profile ${AWS_PROFILE} \
		--region eu-west-1 \
		--stack-name ${STACK_NAME} \
		--template-body file://./cloudformation.yaml \
		--capabilities CAPABILITY_NAMED_IAM CAPABILITY_AUTO_EXPAND \
        --parameters ParameterKey=externalID,ParameterValue=${EXTERNAL_ID}

create-stackset:
	aws cloudformation create-stack-set \
		--profile ${AWS_PROFILE} \
		--region eu-west-1 \
		--stack-set-name ${STACK_NAME}set \
		--template-body file://./stackset-cloudformation.yaml \
		--capabilities CAPABILITY_NAMED_IAM CAPABILITY_AUTO_EXPAND \
        --permission-model SERVICE_MANAGED \
        --auto-deployment Enabled=true,RetainStacksOnAccountRemoval=false \
        --parameters ParameterKey=managementAccountID,ParameterValue=************ ParameterKey=externalID,ParameterValue=${EXTERNAL_ID}

create-stackset-instances:
	aws cloudformation create-stack-instances \
		--profile ${AWS_PROFILE} \
		--region eu-west-1 \
		--regions eu-west-1 \
		--stack-set-name ${STACK_NAME}set \
		--deployment-targets Accounts=************,************,OrganizationalUnitIds=r-8ja1,AccountFilterType=INTERSECTION

update:
	aws cloudformation update-stack \
		--profile ${AWS_PROFILE} \
		--region eu-west-1 \
		--stack-name ${STACK_NAME} \
		--template-body file://./cloudformation.yaml \
		--capabilities CAPABILITY_NAMED_IAM CAPABILITY_AUTO_EXPAND \
        --parameters ParameterKey=externalID,ParameterValue=${EXTERNAL_ID}
