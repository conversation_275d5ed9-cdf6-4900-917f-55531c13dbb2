AWSTemplateFormatVersion: '2010-09-09'
Description: |
  watchTowr CloudFormation template for creating an IAM role for AWS integration

Parameters:
  externalID:
    Type: String
    Description: watchTowr external ID
  managementAccountID:
    Type: String
    Description: Management Account ID

Resources:
  watchTowrIntegrationRoleResource:
    Type: AWS::IAM::Role
    Properties:
      RoleName: "watchTowrAWSConnectorRole"
      Description: "Cross-account read-only access for watchTowr"
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              AWS: !Sub "arn:aws:iam::${managementAccountID}:root"
            Action: sts:AssumeRole
            Condition:
              StringEquals:
                sts:ExternalId: !Sub "${externalID}"
      ManagedPolicyArns:
        - arn:aws:iam::aws:policy/ReadOnlyAccess

Outputs:
  watchTowrRoleARN:
    Description: Role ARN for watchTowrIntegrationRoleResource
    Value: !GetAtt watchTowrIntegrationRoleResource.Arn
  externalID:
    Description: External ID used by the watchTowr Platform To Assume The Role
    Value: !Sub "${externalID}"
