.PHONY: create delete status

AWS_PROFILE ?= $(error Please set the AWS_PROFILE environment variable)
STACK_NAME ?= config-recorder-stack
TEMPLATE_URL ?= https://cf-templates-************.s3.amazonaws.com/config-recorder/stack-instance-template.yaml

# List of all AWS regions
AWS_REGIONS = eu-north-1 eu-west-1 eu-west-2 eu-west-3 eu-central-1 \
			  us-east-1 us-east-2 us-west-1 us-west-2 \
			  ca-central-1 \
              ap-south-1 ap-northeast-1 ap-northeast-2 ap-northeast-3 \
              ap-southeast-1 ap-southeast-2 \
              sa-east-1

create:
	@echo "Deploying Config Recorder to management account in all regions"
	@for region in ${AWS_REGIONS}; do \
		echo "Deploying to $$region"; \
		aws cloudformation create-stack \
			--profile ${AWS_PROFILE} \
			--region $$region \
			--stack-name ${STACK_NAME}-mgmt \
			--template-url ${TEMPLATE_URL} \
			--capabilities CAPABILITY_NAMED_IAM || echo "Deployment failed in $$region"; \
	done

delete:
	@echo "Deleting Config Recorder from management account in all regions"
	@for region in ${AWS_REGIONS}; do \
		echo "Deleting from $$region"; \
		aws cloudformation delete-stack \
			--profile ${AWS_PROFILE} \
			--region $$region \
			--stack-name ${STACK_NAME}-mgmt || echo "Deletion failed in $$region"; \
	done

status:
	@echo "Checking status of Config Recorder stacks in all regions"
	@for region in ${AWS_REGIONS}; do \
		echo "=== Region: $$region ==="; \
		aws cloudformation describe-stacks \
			--profile ${AWS_PROFILE} \
			--region $$region \
			--stack-name ${STACK_NAME}-mgmt \
			--query 'Stacks[0].StackStatus' \
			--output text 2>/dev/null || echo "Stack not found"; \
	done
